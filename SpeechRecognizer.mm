#import <Speech/Speech.h>
#import <AVFoundation/AVFoundation.h>
#include "SpeechRecognizer.h"
#include <iostream>

@interface SpeechRecognizerDelegate : NSObject <SFSpeechRecognizerDelegate>
@end

@implementation SpeechRecognizerDelegate
- (void)speechRecognizer:(SFSpeechRecognizer *)speechRecognizer availabilityDidChange:(BOOL)available
{
    if (available)
    {
        NSLog(@"Speech recognition is available.");
    }
    else
    {
        NSLog(@"Speech recognition is not available.");
    }
}
@end

class SpeechRecognizer::Impl
{
public:
    SFSpeechRecognizer *recognizer;
    AVAudioEngine *audioEngine;
    SFSpeechAudioBufferRecognitionRequest *recognitionRequest;
    SFSpeechRecognitionTask *recognitionTask;
    AVAudioInputNode *inputNode;
    SpeechRecognizerDelegate *delegate;
    SpeechRecognizer::RecognitionCallback callback;

    Impl()
    {
        delegate = [[SpeechRecognizerDelegate alloc] init];
        recognizer = [[SFSpeechRecognizer alloc] initWithLocale:[NSLocale localeWithLocaleIdentifier:@"en-US"]];
        recognizer.delegate = delegate;

        [SFSpeechRecognizer requestAuthorization:^(SFSpeechRecognizerAuthorizationStatus status)
        {
            dispatch_async(dispatch_get_main_queue(), ^{
                switch (status)
                {
                    case SFSpeechRecognizerAuthorizationStatusAuthorized:
                        NSLog(@"Speech recognition authorized.");
                        break;
                    default:
                        NSLog(@"Speech recognition not authorized.");
                        break;
                }
            });
        }];

        audioEngine = [[AVAudioEngine alloc] init];
        inputNode = audioEngine.inputNode;
    }

    ~Impl()
    {
        stopListening();
    }

    void startListening(SpeechRecognizer::RecognitionCallback cb)
    {
        callback = cb;

        // Request microphone permission first
        [[AVAudioSession sharedInstance] requestRecordPermission:^(BOOL granted) {
            if (granted) {
                NSLog(@"Microphone permission granted.");
                dispatch_async(dispatch_get_main_queue(), ^{
                    startRecognitionInternal();
                });
            } else {
                NSLog(@"Microphone permission denied.");
            }
        }];
    }

    void startRecognitionInternal()
    {
        // Configure audio session
        NSError *sessionError = nil;
        AVAudioSession *audioSession = [AVAudioSession sharedInstance];
        [audioSession setCategory:AVAudioSessionCategoryRecord
                             mode:AVAudioSessionModeMeasurement
                          options:AVAudioSessionCategoryOptionDuckOthers
                            error:&sessionError];
        if (sessionError) {
            NSLog(@"Error setting audio session category: %@", sessionError);
            return;
        }

        [audioSession setActive:YES withOptions:AVAudioSessionSetActiveOptionNotifyOthersOnDeactivation error:&sessionError];
        if (sessionError) {
            NSLog(@"Error activating audio session: %@", sessionError);
            return;
        }

        // Clean up any existing recognition task
        if (recognitionTask) {
            [recognitionTask cancel];
            recognitionTask = nil;
        }

        // Remove any existing audio tap
        if ([audioEngine isRunning]) {
            [audioEngine stop];
            [inputNode removeTapOnBus:0];
        }

        recognitionRequest = [[SFSpeechAudioBufferRecognitionRequest alloc] init];
        recognitionRequest.shouldReportPartialResults = YES;

        recognitionTask = [recognizer recognitionTaskWithRequest:recognitionRequest resultHandler:^(SFSpeechRecognitionResult * _Nullable result, NSError * _Nullable error)
        {
            if (result)
            {
                NSString *bestString = result.bestTranscription.formattedString;
                NSLog(@"Speech recognition result: %@", bestString);
                if (callback)
                {
                    // Ensure callback is called on main thread for thread safety
                    dispatch_async(dispatch_get_main_queue(), ^{
                        callback([bestString UTF8String]);
                    });
                }
            }

            if (error)
            {
                NSLog(@"Speech recognition error: %@", error);
                [audioEngine stop];
                if ([inputNode numberOfInputs] > 0) {
                    [inputNode removeTapOnBus:0];
                }
                recognitionRequest = nil;
                recognitionTask = nil;
            }
        }];

        AVAudioFormat *recordingFormat = [inputNode outputFormatForBus:0];
        NSLog(@"Recording format: %@", recordingFormat);

        [inputNode installTapOnBus:0 bufferSize:1024 format:recordingFormat block:^(AVAudioPCMBuffer * _Nonnull buffer, AVAudioTime * _Nonnull when) {
            (void)when; // Suppress unused parameter warning
            [recognitionRequest appendAudioPCMBuffer:buffer];
        }];

        [audioEngine prepare];
        NSError *error = nil;
        BOOL started = [audioEngine startAndReturnError:&error];
        if (error || !started)
        {
            NSLog(@"Error starting audio engine: %@", error);
        }
        else
        {
            NSLog(@"Audio engine started successfully.");
        }
    }

    void stopListening()
    {
        NSLog(@"Stopping speech recognition...");

        if ([audioEngine isRunning])
        {
            [audioEngine stop];
            NSLog(@"Audio engine stopped.");
        }

        if (inputNode && [inputNode numberOfInputs] > 0) {
            [inputNode removeTapOnBus:0];
            NSLog(@"Audio tap removed.");
        }

        if (recognitionRequest)
        {
            [recognitionRequest endAudio];
            recognitionRequest = nil;
            NSLog(@"Recognition request ended.");
        }

        if (recognitionTask)
        {
            [recognitionTask cancel];
            recognitionTask = nil;
            NSLog(@"Recognition task cancelled.");
        }

        // Deactivate audio session
        NSError *sessionError = nil;
        [[AVAudioSession sharedInstance] setActive:NO withOptions:AVAudioSessionSetActiveOptionNotifyOthersOnDeactivation error:&sessionError];
        if (sessionError) {
            NSLog(@"Error deactivating audio session: %@", sessionError);
        }
    }
};



SpeechRecognizer::SpeechRecognizer() : pimpl(new Impl()) {}
SpeechRecognizer::~SpeechRecognizer() { delete pimpl; }
void SpeechRecognizer::startListening(RecognitionCallback callback) { pimpl->startListening(callback); }
void SpeechRecognizer::stopListening() { pimpl->stopListening(); }
